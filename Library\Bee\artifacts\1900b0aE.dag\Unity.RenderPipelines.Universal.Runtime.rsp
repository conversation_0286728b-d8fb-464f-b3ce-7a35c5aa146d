-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-define:UNITY_6000_0_47
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:ENABLE_BURST_1_0_0_OR_NEWER
-define:ENABLE_VR_MODULE
-define:ENABLE_XR_MODULE
-define:USING_ANIMATION_MODULE
-define:USING_PHYSICS2D_MODULE
-define:ENABLE_INPUT_SYSTEM_PACKAGE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@946053a44d19/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@946053a44d19/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@946053a44d19/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@946053a44d19/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.47f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ComponentUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Data/PostProcessData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Data/RenderStateData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Data/UniversalRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Data/UniversalRenderPipelineAsset.DefaultResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Data/UniversalRenderPipelineAssetPrefiltering.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugDisplaySettingsCommon.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugDisplaySettingsLighting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugDisplaySettingsMaterial.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugDisplaySettingsRendering.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugHandler.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/DebugRenderSetup.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/UniversalRenderPipelineDebugDisplaySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/UniversalRenderPipelineDebugDisplayStats.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/UniversalRenderPipelineVolumeDebugSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Debug/UniversalRenderPipelineVolumeDebugSettings.deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DBuffer/DBufferDepthCopyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DBuffer/DBufferRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DBuffer/DecalForwardEmissivePass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DecalDrawErrorRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DecalPreviewPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DecalProjector.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/DecalShaderPassNames.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalChunk.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalCreateDrawCallSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalDrawSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalEntityManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalSkipCulledSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalUpdateCachedSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalUpdateCulledSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/Entities/DecalUpdateCullingGroupSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/ScreenSpace/DecalGBufferRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Decal/ScreenSpace/DecalScreenSpaceRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/DeferredLights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Documentation.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ForwardLights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ForwardRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/Universal2DResourceData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalCameraData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalLightData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalPostProcessingData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalRenderingData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalResourceBase.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalResourceData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/FrameData/UniversalShadowData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/History/RawColorHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/History/RawDepthHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/History/SingleHistoryBase.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/History/StpHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/History/TaaHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/IntermediateTextureMode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/LightCookieManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Memory/BuddyAllocator.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Memory/Fixed2.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Memory/PinnedArray.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/MotionVectors.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/NativeRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/NoAllocUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/NormalReconstruction.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/Bloom.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ChannelMixer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ChromaticAberration.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ColorAdjustments.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ColorCurves.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ColorLookup.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/DepthOfField.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/FilmGrain.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/LensDistortion.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/LiftGammaGain.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/MotionBlur.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/PaniniProjection.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ScreenSpaceLensFlare.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/ShadowsMidtonesHighlights.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/SplitToning.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/Tonemapping.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/Vignette.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Overrides/WhiteBalance.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/AdditionalLightsShadowAtlasLayout.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/AdditionalLightsShadowCasterPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/CapturePass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/ColorGradingLutPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/CopyColorPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/CopyDepthPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DeferredPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DepthNormalOnlyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DepthOnlyPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DrawObjectsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DrawScreenSpaceUIPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/DrawSkyboxPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/FinalBlitPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/GBufferPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/HDRDebugViewPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/InvokeOnRenderObjectCallbackPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/MainLightShadowCasterPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/MotionVectorRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/PostProcessPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/PostProcessPassRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/ProbeVolumeDebugPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/RenderObjectsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/ScreenSpaceAmbientOcclusionPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/ScriptableRenderPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/TransparentSettingsPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/XRDepthMotionPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Passes/XROcclusionMeshPass.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/PostProcessPasses.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/PostProcessUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ReflectionProbeManager.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/DecalRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/DisallowMultipleRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/FullScreenPassRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/FullScreenPassRendererFeature.migration.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/FullScreenPassRendererFeature_OldGUID.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/RenderObjects.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/ScreenSpaceAmbientOcclusion.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RendererFeatures/ScreenSpaceShadows.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderGraph/RenderGraphGraphicsAutomatedTests.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderingLayerUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderingUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/Renderer2DResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRendererResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineDebugShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorAssets.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorMaterials.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineEditorShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeTextures.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderPipelineResources/UniversalRenderPipelineRuntimeXRResources.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderTargetBufferSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RenderTargetHandle.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/RTHandleUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/SampleCount.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/SceneViewDrawMode.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ScriptableRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ScriptableRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ScriptableRendererFeature.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Settings/RenderGraphSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Settings/URPDefaultVolumeProfileSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Settings/URPShaderStrippingSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ShaderBitArray.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ShaderData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ShaderUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ShadowCulling.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/ShadowUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/SpaceFillingCurves.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/StencilUsage.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/StpUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/SupportedOnRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/TemporalAA.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/InclusiveRange.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/LightMinMaxZJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/ReflectionProbeMinMaxZJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/TileRangeExpansionJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/TileSize.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/TilingJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/Tiling/ZBinningJob.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalAdditionalCameraData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalAdditionalLightData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalCameraHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRenderer.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRendererData.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRendererDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRendererRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRenderPipelineCore.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRenderPipelineGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/UniversalRenderPipelineRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/VFXGraph/Utility/PropertyBinders/URPCameraBinder.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/VolumeRequiresRendererFeatures.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/XR/XRPassUniversal.cs"
"Library/PackageCache/com.unity.render-pipelines.universal@39bda2df468a/Runtime/XR/XRSystemUniversal.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"